import{formatIssue as r}from"@effect/schema/ArrayFormatter";import{decodeUnknown as e}from"@effect/schema/ParseResult";import{toNestErrors as o,validateFieldsNatively as s}from"@hookform/resolvers";import*as t from"effect/Effect";const a=(a,m={errors:"all",onExcessProperty:"ignore"})=>(c,f,i)=>e(a,m)(c).pipe(t.catchAll(e=>t.flip(r(e))),t.mapError(r=>{const e=r.reduce((r,e)=>(r[e.path.join(".")]={message:e.message,type:e._tag},r),{});return o(e,i)}),t.tap(()=>t.sync(()=>i.shouldUseNativeValidation&&s({},i))),t.match({onFailure:r=>({errors:r,values:{}}),onSuccess:r=>({errors:{},values:r})}),t.runPromise);export{a as effectTsResolver};
//# sourceMappingURL=effect-ts.modern.mjs.map
