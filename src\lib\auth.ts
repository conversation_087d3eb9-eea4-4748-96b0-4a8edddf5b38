import { User, AuthResponse } from '../types/database';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

export class AuthService {
  private async fetchApi(endpoint: string, options: RequestInit = {}): Promise<any> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || 'API request failed');
    }

    return response.json();
  }

  async signUp(email: string, password: string, fullName?: string): Promise<AuthResponse> {
    try {
      const response = await this.fetchApi('/auth/signup', {
        method: 'POST',
        body: JSON.stringify({ email, password, fullName }),
      });

      return response;
    } catch (error) {
      console.error('SignUp error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to create account' };
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const usersCollection = mongodb.getCollection<User>(Collections.USERS);
      
      // Find user
      const user = await usersCollection.findOne({ email });
      if (!user || !user.password) {
        return { success: false, error: 'Invalid email or password' };
      }

      // Verify password
      const isValidPassword = await this.comparePassword(password, user.password);
      if (!isValidPassword) {
        return { success: false, error: 'Invalid email or password' };
      }

      // Update last login
      await usersCollection.updateOne(
        { _id: user._id },
        { $set: { lastLoginAt: new Date(), updatedAt: new Date() } }
      );

      // Generate token
      const userId = user._id!.toString();
      const token = this.generateToken(userId);

      // Create session
      await this.createSession(userId, token);

      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return {
        success: true,
        user: { ...userWithoutPassword, id: userId },
        token,
      };
    } catch (error) {
      console.error('SignIn error:', error);
      return { success: false, error: 'Failed to sign in' };
    }
  }

  async signOut(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const sessionsCollection = mongodb.getCollection<Session>(Collections.SESSIONS);
      await sessionsCollection.deleteOne({ token });
      return { success: true };
    } catch (error) {
      console.error('SignOut error:', error);
      return { success: false, error: 'Failed to sign out' };
    }
  }

  async getCurrentUser(token: string): Promise<User | null> {
    try {
      const payload = this.verifyToken(token);
      if (!payload) return null;

      const usersCollection = mongodb.getCollection<User>(Collections.USERS);
      const user = await usersCollection.findOne({ _id: new ObjectId(payload.userId) });
      
      if (!user) return null;

      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return { ...userWithoutPassword, id: user._id!.toString() };
    } catch (error) {
      console.error('GetCurrentUser error:', error);
      return null;
    }
  }

  private async createSession(userId: string, token: string): Promise<void> {
    const sessionsCollection = mongodb.getCollection<Session>(Collections.SESSIONS);
    const session: Session = {
      userId,
      token,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      createdAt: new Date(),
    };
    await sessionsCollection.insertOne(session);
  }

  async validateSession(token: string): Promise<boolean> {
    try {
      const sessionsCollection = mongodb.getCollection<Session>(Collections.SESSIONS);
      const session = await sessionsCollection.findOne({ 
        token, 
        expiresAt: { $gt: new Date() } 
      });
      return !!session;
    } catch {
      return false;
    }
  }
}

export const authService = new AuthService();
