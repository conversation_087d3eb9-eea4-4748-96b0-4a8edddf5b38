import { Product, ApiResponse } from '../types/database';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

export class ApiService {
  private async fetchApi(endpoint: string, options: RequestInit = {}): Promise<any> {
    const token = localStorage.getItem('auth_token');
    const headers = {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || 'API request failed');
    }

    return response.json();
  }

  // Product operations
  async saveProduct(product: Omit<Product, '_id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Product>> {
    try {
      const response = await this.fetchApi('/products', {
        method: 'POST',
        body: JSON.stringify(product),
      });

      return response;
    } catch (error) {
      console.error('Save product error:', error);
      return { success: false, error: 'Failed to save product' };
    }
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    try {
      const response = await this.fetchApi(`/products/${id}`);
      return response;
    } catch (error) {
      console.error('Get product error:', error);
      return { success: false, error: 'Failed to get product' };
    }
  }

  async searchProducts(query: string, limit: number = 20): Promise<ApiResponse<Product[]>> {
    try {
      const response = await this.fetchApi(`/products/search?q=${encodeURIComponent(query)}&limit=${limit}`);
      return response;
    } catch (error) {
      console.error('Search products error:', error);
      return { success: false, error: 'Failed to search products' };
    }
  }

  async getProductsByCategory(category: string, limit: number = 20): Promise<ApiResponse<Product[]>> {
    try {
      const response = await this.fetchApi(`/products/category/${encodeURIComponent(category)}?limit=${limit}`);
      return response;
    } catch (error) {
      console.error('Get products by category error:', error);
      return { success: false, error: 'Failed to get products by category' };
    }
  }

  async updateProduct(id: string, updates: Partial<Product>): Promise<ApiResponse<Product>> {
    try {
      const response = await this.fetchApi(`/products/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });
      return response;
    } catch (error) {
      console.error('Update product error:', error);
      return { success: false, error: 'Failed to update product' };
    }
  }

  async deleteProduct(id: string): Promise<ApiResponse<boolean>> {
    try {
      const response = await this.fetchApi(`/products/${id}`, {
        method: 'DELETE',
      });
      return response;
    } catch (error) {
      console.error('Delete product error:', error);
      return { success: false, error: 'Failed to delete product' };
    }
  }

  // Function for external API calls (like Amazon API)
  async fetchExternalProducts(keywords: string, limit: number = 30): Promise<ApiResponse<any[]>> {
    try {
      const response = await this.fetchApi(`/products/external?keywords=${encodeURIComponent(keywords)}&limit=${limit}`);
      return response;
    } catch (error) {
      console.error('Fetch external products error:', error);
      return { success: false, error: 'Failed to fetch external products' };
    }
  }

  // Product comparison functionality
  async compareProducts(productUrls: string[]): Promise<ApiResponse<any>> {
    try {
      const response = await this.fetchApi('/products/compare', {
        method: 'POST',
        body: JSON.stringify({ urls: productUrls }),
      });
      return response;
    } catch (error) {
      console.error('Compare products error:', error);
      return { success: false, error: 'Failed to compare products' };
    }
  }
}

export const apiService = new ApiService();
