{"version": 3, "sources": ["../../src/queriesObserver.ts"], "sourcesContent": ["import { notify<PERSON>ana<PERSON> } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport { replaceEqualDeep } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\n\nfunction difference<T>(array1: Array<T>, array2: Array<T>): Array<T> {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nfunction replaceAt<T>(array: Array<T>, index: number, value: T): Array<T> {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\ntype QueriesObserverListener = (result: Array<QueryObserverResult>) => void\n\ntype CombineFn<TCombinedResult> = (\n  result: Array<QueryObserverResult>,\n) => TCombinedResult\n\nexport interface QueriesObserverOptions<\n  TCombinedResult = Array<QueryObserverResult>,\n> {\n  combine?: CombineFn<TCombinedResult>\n}\n\nexport class QueriesObserver<\n  TCombinedResult = Array<QueryObserverResult>,\n> extends Subscribable<QueriesObserverListener> {\n  #client: QueryClient\n  #result!: Array<QueryObserverResult>\n  #queries: Array<QueryObserverOptions>\n  #options?: QueriesObserverOptions<TCombinedResult>\n  #observers: Array<QueryObserver>\n  #combinedResult?: TCombinedResult\n  #lastCombine?: CombineFn<TCombinedResult>\n  #lastResult?: Array<QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    queries: Array<QueryObserverOptions<any, any, any, any, any>>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n  ) {\n    super()\n\n    this.#client = client\n    this.#options = options\n    this.#queries = []\n    this.#observers = []\n    this.#result = []\n\n    this.setQueries(queries)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: Array<QueryObserverOptions>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.#queries = queries\n    this.#options = options\n\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers\n\n      const newObserverMatches = this.#findMatchingObservers(this.#queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.#observers = newObservers\n      this.#result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n\n      this.#notify()\n    })\n  }\n\n  getCurrentResult(): Array<QueryObserverResult> {\n    return this.#result\n  }\n\n  getQueries() {\n    return this.#observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.#observers\n  }\n\n  getOptimisticResult(\n    queries: Array<QueryObserverOptions>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): [\n    rawResult: Array<QueryObserverResult>,\n    combineResult: (r?: Array<QueryObserverResult>) => TCombinedResult,\n    trackResult: () => Array<QueryObserverResult>,\n  ] {\n    const matches = this.#findMatchingObservers(queries)\n    const result = matches.map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n\n    return [\n      result,\n      (r?: Array<QueryObserverResult>) => {\n        return this.#combineResult(r ?? result, combine)\n      },\n      () => {\n        return matches.map((match, index) => {\n          const observerResult = result[index]!\n          return !match.defaultedQueryOptions.notifyOnChangeProps\n            ? match.observer.trackResult(observerResult, (accessedProp) => {\n                // track property on all observers to ensure proper (synchronized) tracking (#7000)\n                matches.forEach((m) => {\n                  m.observer.trackProp(accessedProp)\n                })\n              })\n            : observerResult\n        })\n      },\n    ]\n  }\n\n  #combineResult(\n    input: Array<QueryObserverResult>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): TCombinedResult {\n    if (combine) {\n      if (\n        !this.#combinedResult ||\n        this.#result !== this.#lastResult ||\n        combine !== this.#lastCombine\n      ) {\n        this.#lastCombine = combine\n        this.#lastResult = this.#result\n        this.#combinedResult = replaceEqualDeep(\n          this.#combinedResult,\n          combine(input),\n        )\n      }\n\n      return this.#combinedResult\n    }\n    return input as any\n  }\n\n  #findMatchingObservers(\n    queries: Array<QueryObserverOptions>,\n  ): Array<QueryObserverMatch> {\n    const prevObserversMap = new Map(\n      this.#observers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const observers: Array<QueryObserverMatch> = []\n\n    queries.forEach((options) => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options)\n      const match = prevObserversMap.get(defaultedOptions.queryHash)\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match,\n        })\n      } else {\n        const existingObserver = this.#observers.find(\n          (o) => o.options.queryHash === defaultedOptions.queryHash,\n        )\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer:\n            existingObserver ??\n            new QueryObserver(this.#client, defaultedOptions),\n        })\n      }\n    })\n\n    return observers.sort((a, b) => {\n      return (\n        queries.findIndex(\n          (q) => q.queryHash === a.defaultedQueryOptions.queryHash,\n        ) -\n        queries.findIndex(\n          (q) => q.queryHash === b.defaultedQueryOptions.queryHash,\n        )\n      )\n    })\n  }\n\n  #onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.#observers.indexOf(observer)\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result)\n      this.#notify()\n    }\n  }\n\n  #notify(): void {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult\n      const newResult = this.#combineResult(\n        this.#result,\n        this.#options?.combine,\n      )\n\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach((listener) => {\n            listener(this.#result)\n          })\n        })\n      }\n    }\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAA8B;AAC9B,2BAA8B;AAC9B,0BAA6B;AAC7B,mBAAiC;AASjC,SAAS,WAAc,QAAkB,QAA4B;AACnE,SAAO,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,SAAS,CAAC,CAAC;AACjD;AAEA,SAAS,UAAa,OAAiB,OAAe,OAAoB;AACxE,QAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,OAAK,KAAK,IAAI;AACd,SAAO;AACT;AApBA;AAkCO,IAAM,kBAAN,cAEG,iCAAsC;AAAA,EAU9C,YACE,QACA,SACA,SACA;AACA,UAAM;AAoIR;AAuBA;AA0CA;AAQA;AA3NA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASE,uBAAK,SAAU;AACf,uBAAK,UAAW;AAChB,uBAAK,UAAW,CAAC;AACjB,uBAAK,YAAa,CAAC;AACnB,uBAAK,SAAU,CAAC;AAEhB,SAAK,WAAW,OAAO;AAAA,EACzB;AAAA,EAEU,cAAoB;AAC5B,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,yBAAK,YAAW,QAAQ,CAAC,aAAa;AACpC,iBAAS,UAAU,CAAC,WAAW;AAC7B,gCAAK,wBAAL,WAAe,UAAU;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEU,gBAAsB;AAC9B,QAAI,CAAC,KAAK,UAAU,MAAM;AACxB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EAEA,UAAgB;AACd,SAAK,YAAY,oBAAI,IAAI;AACzB,uBAAK,YAAW,QAAQ,CAAC,aAAa;AACpC,eAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EAEA,WACE,SACA,SACA,eACM;AACN,uBAAK,UAAW;AAChB,uBAAK,UAAW;AAEhB,uCAAc,MAAM,MAAM;AACxB,YAAM,gBAAgB,mBAAK;AAE3B,YAAM,qBAAqB,sBAAK,kDAAL,WAA4B,mBAAK;AAG5D,yBAAmB;AAAA,QAAQ,CAAC,UAC1B,MAAM,SAAS,WAAW,MAAM,uBAAuB,aAAa;AAAA,MACtE;AAEA,YAAM,eAAe,mBAAmB,IAAI,CAAC,UAAU,MAAM,QAAQ;AACrE,YAAM,YAAY,aAAa;AAAA,QAAI,CAAC,aAClC,SAAS,iBAAiB;AAAA,MAC5B;AAEA,YAAM,iBAAiB,aAAa;AAAA,QAClC,CAAC,UAAU,UAAU,aAAa,cAAc,KAAK;AAAA,MACvD;AAEA,UAAI,cAAc,WAAW,aAAa,UAAU,CAAC,gBAAgB;AACnE;AAAA,MACF;AAEA,yBAAK,YAAa;AAClB,yBAAK,SAAU;AAEf,UAAI,CAAC,KAAK,aAAa,GAAG;AACxB;AAAA,MACF;AAEA,iBAAW,eAAe,YAAY,EAAE,QAAQ,CAAC,aAAa;AAC5D,iBAAS,QAAQ;AAAA,MACnB,CAAC;AAED,iBAAW,cAAc,aAAa,EAAE,QAAQ,CAAC,aAAa;AAC5D,iBAAS,UAAU,CAAC,WAAW;AAC7B,gCAAK,wBAAL,WAAe,UAAU;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAED,4BAAK,oBAAL;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,mBAA+C;AAC7C,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,aAAa;AACX,WAAO,mBAAK,YAAW,IAAI,CAAC,aAAa,SAAS,gBAAgB,CAAC;AAAA,EACrE;AAAA,EAEA,eAAe;AACb,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,oBACE,SACA,SAKA;AACA,UAAM,UAAU,sBAAK,kDAAL,WAA4B;AAC5C,UAAM,SAAS,QAAQ;AAAA,MAAI,CAAC,UAC1B,MAAM,SAAS,oBAAoB,MAAM,qBAAqB;AAAA,IAChE;AAEA,WAAO;AAAA,MACL;AAAA,MACA,CAAC,MAAmC;AAClC,eAAO,sBAAK,kCAAL,WAAoB,KAAK,QAAQ;AAAA,MAC1C;AAAA,MACA,MAAM;AACJ,eAAO,QAAQ,IAAI,CAAC,OAAO,UAAU;AACnC,gBAAM,iBAAiB,OAAO,KAAK;AACnC,iBAAO,CAAC,MAAM,sBAAsB,sBAChC,MAAM,SAAS,YAAY,gBAAgB,CAAC,iBAAiB;AAE3D,oBAAQ,QAAQ,CAAC,MAAM;AACrB,gBAAE,SAAS,UAAU,YAAY;AAAA,YACnC,CAAC;AAAA,UACH,CAAC,IACD;AAAA,QACN,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AA4FF;AA5OE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA2IA;AAAA,mBAAc,SACZ,OACA,SACiB;AACjB,MAAI,SAAS;AACX,QACE,CAAC,mBAAK,oBACN,mBAAK,aAAY,mBAAK,gBACtB,YAAY,mBAAK,eACjB;AACA,yBAAK,cAAe;AACpB,yBAAK,aAAc,mBAAK;AACxB,yBAAK,qBAAkB;AAAA,QACrB,mBAAK;AAAA,QACL,QAAQ,KAAK;AAAA,MACf;AAAA,IACF;AAEA,WAAO,mBAAK;AAAA,EACd;AACA,SAAO;AACT;AAEA;AAAA,2BAAsB,SACpB,SAC2B;AAC3B,QAAM,mBAAmB,IAAI;AAAA,IAC3B,mBAAK,YAAW,IAAI,CAAC,aAAa,CAAC,SAAS,QAAQ,WAAW,QAAQ,CAAC;AAAA,EAC1E;AAEA,QAAM,YAAuC,CAAC;AAE9C,UAAQ,QAAQ,CAAC,YAAY;AAC3B,UAAM,mBAAmB,mBAAK,SAAQ,oBAAoB,OAAO;AACjE,UAAM,QAAQ,iBAAiB,IAAI,iBAAiB,SAAS;AAC7D,QAAI,OAAO;AACT,gBAAU,KAAK;AAAA,QACb,uBAAuB;AAAA,QACvB,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,OAAO;AACL,YAAM,mBAAmB,mBAAK,YAAW;AAAA,QACvC,CAAC,MAAM,EAAE,QAAQ,cAAc,iBAAiB;AAAA,MAClD;AACA,gBAAU,KAAK;AAAA,QACb,uBAAuB;AAAA,QACvB,UACE,oBACA,IAAI,mCAAc,mBAAK,UAAS,gBAAgB;AAAA,MACpD,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,SAAO,UAAU,KAAK,CAAC,GAAG,MAAM;AAC9B,WACE,QAAQ;AAAA,MACN,CAAC,MAAM,EAAE,cAAc,EAAE,sBAAsB;AAAA,IACjD,IACA,QAAQ;AAAA,MACN,CAAC,MAAM,EAAE,cAAc,EAAE,sBAAsB;AAAA,IACjD;AAAA,EAEJ,CAAC;AACH;AAEA;AAAA,cAAS,SAAC,UAAyB,QAAmC;AACpE,QAAM,QAAQ,mBAAK,YAAW,QAAQ,QAAQ;AAC9C,MAAI,UAAU,IAAI;AAChB,uBAAK,SAAU,UAAU,mBAAK,UAAS,OAAO,MAAM;AACpD,0BAAK,oBAAL;AAAA,EACF;AACF;AAEA;AAAA,YAAO,WAAS;AAhQlB;AAiQI,MAAI,KAAK,aAAa,GAAG;AACvB,UAAM,iBAAiB,mBAAK;AAC5B,UAAM,YAAY,sBAAK,kCAAL,WAChB,mBAAK,WACL,wBAAK,cAAL,mBAAe;AAGjB,QAAI,mBAAmB,WAAW;AAChC,yCAAc,MAAM,MAAM;AACxB,aAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,mBAAS,mBAAK,QAAO;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": []}