{"version": 3, "sources": ["../../src/mutationCache.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { DefaultError, MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: DefaultError,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\nexport type MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  #mutations: Map<string, Array<Mutation<any, any, any, any>>>\n  #mutationId: number\n\n  constructor(public config: MutationCacheConfig = {}) {\n    super()\n    this.#mutations = new Map()\n    this.#mutationId = Date.now()\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    const scope = scopeFor(mutation)\n    const mutations = this.#mutations.get(scope) ?? []\n    mutations.push(mutation)\n    this.#mutations.set(scope, mutations)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    const scope = scopeFor(mutation)\n    if (this.#mutations.has(scope)) {\n      const mutations = this.#mutations\n        .get(scope)\n        ?.filter((x) => x !== mutation)\n      if (mutations) {\n        if (mutations.length === 0) {\n          this.#mutations.delete(scope)\n        } else {\n          this.#mutations.set(scope, mutations)\n        }\n      }\n    }\n\n    this.notify({ type: 'removed', mutation })\n  }\n\n  canRun(mutation: Mutation<any, any, any, any>): boolean {\n    const firstPendingMutation = this.#mutations\n      .get(scopeFor(mutation))\n      ?.find((m) => m.state.status === 'pending')\n\n    // we can run if there is no current pending mutation (start use-case)\n    // or if WE are the first pending mutation (continue use-case)\n    return !firstPendingMutation || firstPendingMutation === mutation\n  }\n\n  runNext(mutation: Mutation<any, any, any, any>): Promise<unknown> {\n    const foundMutation = this.#mutations\n      .get(scopeFor(mutation))\n      ?.find((m) => m !== mutation && m.state.isPaused)\n\n    return foundMutation?.continue() ?? Promise.resolve()\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Array<Mutation> {\n    return [...this.#mutations.values()].flat()\n  }\n\n  find<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = any,\n    TContext = unknown,\n  >(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((mutation) =>\n      matchMutation(defaultedFilters, mutation),\n    ) as Mutation<TData, TError, TVariables, TContext> | undefined\n  }\n\n  findAll(filters: MutationFilters = {}): Array<Mutation> {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused)\n\n    return notifyManager.batch(() =>\n      Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop)),\n      ),\n    )\n  }\n}\n\nfunction scopeFor(mutation: Mutation<any, any, any, any>) {\n  return mutation.options.scope?.id ?? String(mutation.mutationId)\n}\n"], "mappings": ";AAAA,SAAS,qBAAqB;AAC9B,SAAS,gBAAgB;AACzB,SAAS,eAAe,YAAY;AACpC,SAAS,oBAAoB;AAgFtB,IAAM,gBAAN,cAA4B,aAAoC;AAAA,EAIrE,YAAmB,SAA8B,CAAC,GAAG;AACnD,UAAM;AADW;AAEjB,SAAK,aAAa,oBAAI,IAAI;AAC1B,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B;AAAA,EAPA;AAAA,EACA;AAAA,EAQA,MACE,QACA,SACA,OAC+C;AAC/C,UAAM,WAAW,IAAI,SAAS;AAAA,MAC5B,eAAe;AAAA,MACf,YAAY,EAAE,KAAK;AAAA,MACnB,SAAS,OAAO,uBAAuB,OAAO;AAAA,MAC9C;AAAA,IACF,CAAC;AAED,SAAK,IAAI,QAAQ;AAEjB,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,UAA8C;AAChD,UAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAM,YAAY,KAAK,WAAW,IAAI,KAAK,KAAK,CAAC;AACjD,cAAU,KAAK,QAAQ;AACvB,SAAK,WAAW,IAAI,OAAO,SAAS;AACpC,SAAK,OAAO,EAAE,MAAM,SAAS,SAAS,CAAC;AAAA,EACzC;AAAA,EAEA,OAAO,UAA8C;AACnD,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,KAAK,WAAW,IAAI,KAAK,GAAG;AAC9B,YAAM,YAAY,KAAK,WACpB,IAAI,KAAK,GACR,OAAO,CAAC,MAAM,MAAM,QAAQ;AAChC,UAAI,WAAW;AACb,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,WAAW,OAAO,KAAK;AAAA,QAC9B,OAAO;AACL,eAAK,WAAW,IAAI,OAAO,SAAS;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAEA,SAAK,OAAO,EAAE,MAAM,WAAW,SAAS,CAAC;AAAA,EAC3C;AAAA,EAEA,OAAO,UAAiD;AACtD,UAAM,uBAAuB,KAAK,WAC/B,IAAI,SAAS,QAAQ,CAAC,GACrB,KAAK,CAAC,MAAM,EAAE,MAAM,WAAW,SAAS;AAI5C,WAAO,CAAC,wBAAwB,yBAAyB;AAAA,EAC3D;AAAA,EAEA,QAAQ,UAA0D;AAChE,UAAM,gBAAgB,KAAK,WACxB,IAAI,SAAS,QAAQ,CAAC,GACrB,KAAK,CAAC,MAAM,MAAM,YAAY,EAAE,MAAM,QAAQ;AAElD,WAAO,eAAe,SAAS,KAAK,QAAQ,QAAQ;AAAA,EACtD;AAAA,EAEA,QAAc;AACZ,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAO,EAAE,QAAQ,CAAC,aAAa;AAClC,aAAK,OAAO,QAAQ;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,SAA0B;AACxB,WAAO,CAAC,GAAG,KAAK,WAAW,OAAO,CAAC,EAAE,KAAK;AAAA,EAC5C;AAAA,EAEA,KAME,SAC2D;AAC3D,UAAM,mBAAmB,EAAE,OAAO,MAAM,GAAG,QAAQ;AAEnD,WAAO,KAAK,OAAO,EAAE;AAAA,MAAK,CAAC,aACzB,cAAc,kBAAkB,QAAQ;AAAA,IAC1C;AAAA,EACF;AAAA,EAEA,QAAQ,UAA2B,CAAC,GAAoB;AACtD,WAAO,KAAK,OAAO,EAAE,OAAO,CAAC,aAAa,cAAc,SAAS,QAAQ,CAAC;AAAA,EAC5E;AAAA,EAEA,OAAO,OAAiC;AACtC,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,wBAA0C;AACxC,UAAM,kBAAkB,KAAK,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,QAAQ;AAEpE,WAAO,cAAc;AAAA,MAAM,MACzB,QAAQ;AAAA,QACN,gBAAgB,IAAI,CAAC,aAAa,SAAS,SAAS,EAAE,MAAM,IAAI,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,SAAS,UAAwC;AACxD,SAAO,SAAS,QAAQ,OAAO,MAAM,OAAO,SAAS,UAAU;AACjE;", "names": []}