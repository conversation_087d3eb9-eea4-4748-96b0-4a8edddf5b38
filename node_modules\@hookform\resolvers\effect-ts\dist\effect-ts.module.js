import{formatIssue as r}from"@effect/schema/ArrayFormatter";import{decodeUnknown as e}from"@effect/schema/ParseResult";import{toNestErrors as t,validateFieldsNatively as o}from"@hookform/resolvers";import*as n from"effect/Effect";var u=function(u,s){return void 0===s&&(s={errors:"all",onExcessProperty:"ignore"}),function(a,c,f){return e(u,s)(a).pipe(n.catchAll(function(e){return n.flip(r(e))}),n.mapError(function(r){var e=r.reduce(function(r,e){return r[e.path.join(".")]={message:e.message,type:e._tag},r},{});return t(e,f)}),n.tap(function(){return n.sync(function(){return f.shouldUseNativeValidation&&o({},f)})}),n.match({onFailure:function(r){return{errors:r,values:{}}},onSuccess:function(r){return{errors:{},values:r}}}),n.runPromise)}};export{u as effectTsResolver};
//# sourceMappingURL=effect-ts.module.js.map
