import { ObjectId } from 'mongodb';

// User types
export interface User {
  _id?: ObjectId;
  id?: string;
  email: string;
  password?: string; // hashed password
  fullName?: string;
  avatarUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  emailVerified?: boolean;
  lastLoginAt?: Date;
}

export interface UserProfile {
  _id?: ObjectId;
  userId: string;
  fullName?: string;
  avatarUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Product types
export interface Product {
  _id?: ObjectId;
  id?: string;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  imageUrl?: string;
  category?: string;
  brand?: string;
  rating?: number;
  reviewCount?: number;
  availability: boolean;
  platform: string;
  platformProductId: string;
  url: string;
  createdAt: Date;
  updatedAt: Date;
}

// Session types for authentication
export interface Session {
  _id?: ObjectId;
  userId: string;
  token: string;
  expiresAt: Date;
  createdAt: Date;
  userAgent?: string;
  ipAddress?: string;
}

// Search history
export interface SearchHistory {
  _id?: ObjectId;
  userId?: string;
  query: string;
  results?: Product[];
  createdAt: Date;
}

// Product comparison
export interface ProductComparison {
  _id?: ObjectId;
  userId?: string;
  products: Product[];
  createdAt: Date;
  updatedAt: Date;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
}

// Collection names
export const Collections = {
  USERS: 'users',
  PROFILES: 'profiles',
  PRODUCTS: 'products',
  SESSIONS: 'sessions',
  SEARCH_HISTORY: 'searchHistory',
  PRODUCT_COMPARISONS: 'productComparisons',
} as const;
