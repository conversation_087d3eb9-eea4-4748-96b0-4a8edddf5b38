const express = require('express');
const cors = require('cors');
const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = process.env.PORT || 3001;

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/';
const DB_NAME = process.env.DB_NAME || 'shopwise';
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

let db;

// Connect to MongoDB
MongoClient.connect(MONGODB_URI)
  .then(client => {
    console.log('Connected to MongoDB');
    db = client.db(DB_NAME);
  })
  .catch(error => {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  });

// Middleware
app.use(cors());
app.use(express.json());

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

// Auth routes
app.post('/api/auth/signup', async (req, res) => {
  try {
    const { email, password, fullName } = req.body;

    // Check if user exists
    const existingUser = await db.collection('users').findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const result = await db.collection('users').insertOne({
      email,
      password: hashedPassword,
      fullName,
      createdAt: new Date(),
      updatedAt: new Date(),
      emailVerified: false,
    });

    // Generate token
    const token = jwt.sign({ userId: result.insertedId.toString() }, JWT_SECRET, { expiresIn: '7d' });

    // Create session
    await db.collection('sessions').insertOne({
      userId: result.insertedId.toString(),
      token,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      createdAt: new Date(),
    });

    res.json({
      success: true,
      user: { id: result.insertedId.toString(), email, fullName },
      token,
    });
  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/auth/signin', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await db.collection('users').findOne({ email });
    if (!user) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Generate token
    const token = jwt.sign({ userId: user._id.toString() }, JWT_SECRET, { expiresIn: '7d' });

    // Create session
    await db.collection('sessions').insertOne({
      userId: user._id.toString(),
      token,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      createdAt: new Date(),
    });

    // Update last login
    await db.collection('users').updateOne(
      { _id: user._id },
      { $set: { lastLoginAt: new Date(), updatedAt: new Date() } }
    );

    res.json({
      success: true,
      user: { id: user._id.toString(), email: user.email, fullName: user.fullName },
      token,
    });
  } catch (error) {
    console.error('Signin error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/auth/signout', authenticateToken, async (req, res) => {
  try {
    const token = req.headers['authorization'].split(' ')[1];
    await db.collection('sessions').deleteOne({ token });
    res.json({ success: true });
  } catch (error) {
    console.error('Signout error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    const user = await db.collection('users').findOne(
      { _id: new require('mongodb').ObjectId(req.user.userId) },
      { projection: { password: 0 } }
    );
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      success: true,
      user: { id: user._id.toString(), email: user.email, fullName: user.fullName },
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Product routes
app.get('/api/products/search', async (req, res) => {
  try {
    const { q, limit = 20 } = req.query;
    
    // Mock product search - in real app, this would search your database
    const mockProducts = Array.from({ length: Math.min(limit, 10) }, (_, i) => ({
      id: `product-${i + 1}`,
      name: `${q} Product ${i + 1}`,
      price: Math.floor(Math.random() * 1000) + 100,
      originalPrice: Math.floor(Math.random() * 1200) + 150,
      currency: 'USD',
      imageUrl: `https://placehold.co/300x300/f8f8f8/878787?text=Product+${i + 1}`,
      rating: Math.floor(Math.random() * 5) + 1,
      reviewCount: Math.floor(Math.random() * 1000) + 10,
      availability: Math.random() > 0.2,
      platform: 'Amazon',
      url: `https://amazon.com/product-${i + 1}`,
    }));

    res.json({ success: true, data: mockProducts });
  } catch (error) {
    console.error('Search products error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/products/external', async (req, res) => {
  try {
    const { keywords = 'electronics', limit = 30 } = req.query;
    
    // Mock external API call - in real app, this would call external APIs
    const mockProducts = Array.from({ length: Math.min(limit, 10) }, (_, i) => ({
      id: `external-${i + 1}`,
      name: `${keywords} Product ${i + 1}`,
      price: Math.floor(Math.random() * 1000) + 100,
      originalPrice: Math.floor(Math.random() * 1200) + 150,
      currency: 'USD',
      imageUrl: `https://placehold.co/300x300/f8f8f8/878787?text=Product+${i + 1}`,
      rating: Math.floor(Math.random() * 5) + 1,
      reviewCount: Math.floor(Math.random() * 1000) + 10,
      availability: Math.random() > 0.2,
      platform: 'Amazon',
      url: `https://amazon.com/product-${i + 1}`,
    }));

    res.json({ success: true, data: mockProducts });
  } catch (error) {
    console.error('Fetch external products error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/products/compare', async (req, res) => {
  try {
    const { urls } = req.body;
    
    // Mock product comparison
    const mockComparison = {
      products: urls.map((url, i) => ({
        url,
        name: `Product from ${new URL(url).hostname}`,
        price: Math.floor(Math.random() * 1000) + 100,
        rating: Math.floor(Math.random() * 5) + 1,
        availability: Math.random() > 0.2,
      })),
      cheapest: 0,
      bestRated: 1,
    };

    res.json({ success: true, data: mockComparison });
  } catch (error) {
    console.error('Compare products error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
